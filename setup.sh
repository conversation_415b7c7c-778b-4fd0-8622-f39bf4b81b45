#!/bin/bash

# Blind XSS Scanner Setup Script
# This script helps set up the environment for the Blind XSS Scanner

set -e

echo "🔧 Blind XSS Scanner Setup"
echo "=========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if Go is installed
check_go() {
    if command -v go &> /dev/null; then
        print_status "Go is installed: $(go version)"
        return 0
    else
        print_error "Go is not installed"
        echo "Please install Go from https://golang.org/dl/"
        return 1
    fi
}

# Install GAU
install_gau() {
    echo "Installing GAU (GetAllUrls)..."
    
    if command -v gau &> /dev/null; then
        print_warning "GAU is already installed: $(gau --version 2>/dev/null || echo 'version unknown')"
        read -p "Do you want to reinstall GAU? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            return 0
        fi
    fi
    
    if check_go; then
        echo "Installing GAU..."
        go install github.com/lc/gau/v2/cmd/gau@latest
        
        # Add Go bin to PATH if not already there
        if [[ ":$PATH:" != *":$HOME/go/bin:"* ]]; then
            echo "Adding Go bin directory to PATH..."
            echo 'export PATH=$PATH:$HOME/go/bin' >> ~/.bashrc
            export PATH=$PATH:$HOME/go/bin
        fi
        
        if command -v gau &> /dev/null; then
            print_status "GAU installed successfully"
        else
            print_error "GAU installation failed"
            return 1
        fi
    else
        return 1
    fi
}

# Install Python dependencies
install_python_deps() {
    echo "Installing Python dependencies..."
    
    if command -v python3 &> /dev/null; then
        print_status "Python3 is available: $(python3 --version)"
    else
        print_error "Python3 is not installed"
        echo "Please install Python3 from https://python.org/"
        return 1
    fi
    
    if command -v pip3 &> /dev/null; then
        print_status "pip3 is available"
    else
        print_error "pip3 is not installed"
        echo "Please install pip3"
        return 1
    fi
    
    echo "Installing Python packages..."
    pip3 install -r requirements.txt
    print_status "Python dependencies installed"
}

# Make scripts executable
make_executable() {
    echo "Making scripts executable..."
    chmod +x blind_xss_scanner.py
    chmod +x test_scanner.py
    chmod +x setup.sh
    print_status "Scripts made executable"
}

# Create sample configuration
create_sample_config() {
    echo "Creating sample configuration files..."
    
    # Check if files already exist
    if [[ -f "domain.txt" ]]; then
        print_warning "domain.txt already exists"
    else
        echo "Creating sample domain.txt..."
        cat > domain.txt << EOF
# Add your target domains here (one per line)
# Example:
# example.com
# testsite.org
# Remove this comment and add real domains
EOF
        print_status "Created sample domain.txt"
    fi
    
    if [[ -f "headers.txt" ]]; then
        print_warning "headers.txt already exists"
    else
        echo "Creating sample headers.txt..."
        cat > headers.txt << EOF
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
X-Forwarded-For: 127.0.0.1
X-Real-IP: ***********
X-Originating-IP: ********
X-Remote-IP: **********
X-Client-IP: ***********
X-Forwarded-Host: localhost
Referer: https://google.com
X-Custom-Header: test-value
Accept: text/html,application/xhtml+xml,application/xml
Accept-Language: en-US,en;q=0.9
Accept-Encoding: gzip, deflate
Connection: keep-alive
EOF
        print_status "Created sample headers.txt"
    fi
}

# Run tests
run_tests() {
    echo "Running tests..."
    if python3 test_scanner.py; then
        print_status "All tests passed!"
    else
        print_warning "Some tests failed. Please check the output above."
    fi
}

# Main setup process
main() {
    echo "Starting setup process..."
    echo
    
    # Install GAU
    if ! install_gau; then
        print_error "Failed to install GAU"
        exit 1
    fi
    echo
    
    # Install Python dependencies
    if ! install_python_deps; then
        print_error "Failed to install Python dependencies"
        exit 1
    fi
    echo
    
    # Make scripts executable
    make_executable
    echo
    
    # Create sample configuration
    create_sample_config
    echo
    
    # Run tests
    run_tests
    echo
    
    echo "🎉 Setup completed!"
    echo
    echo "Next steps:"
    echo "1. Edit domain.txt to add your target domains"
    echo "2. Modify headers.txt if needed"
    echo "3. Update the XSS payload in blind_xss_scanner.py or use --payload option"
    echo "4. Run the scanner: python3 blind_xss_scanner.py"
    echo
    echo "For help: python3 blind_xss_scanner.py --help"
}

# Check if running as root (not recommended)
if [[ $EUID -eq 0 ]]; then
    print_warning "Running as root is not recommended"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Run main setup
main
