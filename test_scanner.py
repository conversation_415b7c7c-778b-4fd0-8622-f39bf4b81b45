#!/usr/bin/env python3
"""
Test script for Blind XSS Scanner
This script performs basic validation and testing of the scanner components
"""

import os
import sys
import subprocess
import requests
from blind_xss_scanner import BlindXSSScanner

def test_dependencies():
    """Test if required dependencies are available"""
    print("Testing dependencies...")
    
    # Test GAU
    try:
        result = subprocess.run(['gau', '--help'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ GAU tool is available")
        else:
            print("✗ GAU tool failed")
            return False
    except FileNotFoundError:
        print("✗ GAU tool not found. Please install GAU first.")
        print("  Install with: go install github.com/lc/gau/v2/cmd/gau@latest")
        return False
    except Exception as e:
        print(f"✗ Error testing GAU: {e}")
        return False
    
    # Test Python requests
    try:
        import requests
        print("✓ Python requests library is available")
    except ImportError:
        print("✗ Python requests library not found")
        print("  Install with: pip install requests")
        return False
    
    return True

def test_input_files():
    """Test if input files exist and are readable"""
    print("\nTesting input files...")
    
    files_to_check = ['domain.txt', 'headers.txt']
    
    for filename in files_to_check:
        if os.path.exists(filename):
            try:
                with open(filename, 'r') as f:
                    content = f.read().strip()
                    if content:
                        lines = len(content.split('\n'))
                        print(f"✓ {filename} exists and contains {lines} lines")
                    else:
                        print(f"⚠ {filename} exists but is empty")
            except Exception as e:
                print(f"✗ Error reading {filename}: {e}")
                return False
        else:
            print(f"✗ {filename} not found")
            return False
    
    return True

def test_gau_functionality():
    """Test GAU with a safe domain"""
    print("\nTesting GAU functionality...")
    
    try:
        # Test with a safe, well-known domain
        test_domain = "httpbin.org"
        print(f"Testing GAU with domain: {test_domain}")
        
        result = subprocess.run(['gau', test_domain], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            urls = [url.strip() for url in result.stdout.split('\n') if url.strip()]
            print(f"✓ GAU returned {len(urls)} URLs for {test_domain}")
            
            # Show first few URLs as examples
            if urls:
                print("  Sample URLs:")
                for url in urls[:3]:
                    print(f"    {url}")
            
            return True
        else:
            print(f"✗ GAU failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ GAU test timed out")
        return False
    except Exception as e:
        print(f"✗ Error testing GAU: {e}")
        return False

def test_http_functionality():
    """Test HTTP functionality with a safe endpoint"""
    print("\nTesting HTTP functionality...")
    
    try:
        # Test with httpbin.org which is designed for testing
        test_url = "https://httpbin.org/headers"
        test_headers = {
            'User-Agent': 'BlindXSSScanner-Test',
            'X-Test-Header': 'test-value'
        }
        
        response = requests.get(test_url, headers=test_headers, timeout=10)
        
        if response.status_code == 200:
            print(f"✓ HTTP request successful (status: {response.status_code})")
            return True
        else:
            print(f"⚠ HTTP request returned status: {response.status_code}")
            return True  # Still consider this a pass
            
    except Exception as e:
        print(f"✗ HTTP test failed: {e}")
        return False

def test_scanner_initialization():
    """Test scanner initialization"""
    print("\nTesting scanner initialization...")
    
    try:
        scanner = BlindXSSScanner(
            domains_file='domain.txt',
            headers_file='headers.txt',
            output_file='test_results.json',
            payload='<script>alert("test")</script>',
            rate_limit=0.1,
            timeout=5,
            test_mode='individual',
            max_workers=2
        )
        
        print("✓ Scanner initialized successfully")
        
        # Test reading domains
        domains = scanner.read_domains()
        if domains:
            print(f"✓ Successfully read {len(domains)} domains")
        else:
            print("⚠ No domains found")
        
        # Test reading headers
        headers = scanner.read_headers()
        if headers:
            print(f"✓ Successfully read {len(headers)} headers")
        else:
            print("⚠ No headers found")
        
        return True
        
    except Exception as e:
        print(f"✗ Scanner initialization failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("Blind XSS Scanner - Test Suite")
    print("=" * 40)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Input Files", test_input_files),
        ("GAU Functionality", test_gau_functionality),
        ("HTTP Functionality", test_http_functionality),
        ("Scanner Initialization", test_scanner_initialization),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * len(test_name))
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("TEST SUMMARY")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The scanner is ready to use.")
        return True
    else:
        print("⚠ Some tests failed. Please address the issues before using the scanner.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
