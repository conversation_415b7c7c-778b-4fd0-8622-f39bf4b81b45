#!/usr/bin/env python3
"""
Example script to run Blind XSS Scanner with Burp Suite proxy
This script demonstrates how to use the scanner with Burp Suite for detailed analysis
"""

import subprocess
import sys
import socket
import time

def check_burp_proxy(host='127.0.0.1', port=8080):
    """Check if Burp Suite proxy is running"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception:
        return False

def main():
    print("🔍 Blind XSS Scanner with Burp Suite Integration")
    print("=" * 50)
    
    # Check if Burp proxy is running
    burp_proxy = "http://127.0.0.1:8080"
    
    if check_burp_proxy():
        print(f"✓ Burp Suite proxy detected at {burp_proxy}")
    else:
        print(f"⚠ Burp Suite proxy not detected at {burp_proxy}")
        print("\nTo use Burp Suite proxy:")
        print("1. Start Burp Suite")
        print("2. Go to Proxy → Options")
        print("3. Ensure listener is running on 127.0.0.1:8080")
        print("4. Run this script again")
        
        response = input("\nContinue without proxy? (y/N): ")
        if response.lower() != 'y':
            sys.exit(1)
        burp_proxy = None
    
    # Prepare command
    cmd = [
        'python3', 'blind_xss_scanner.py',
        '--domains', 'domain.txt',
        '--headers', 'headers.txt',
        '--output', 'burp_results.json',
        '--rate-limit', '2.0',
        '--timeout', '15',
        '--mode', 'individual',
        '--workers', '3'
    ]
    
    # Add proxy if available
    if burp_proxy:
        cmd.extend(['--proxy', burp_proxy])
        print(f"🚀 Starting scanner with Burp proxy: {burp_proxy}")
    else:
        print("🚀 Starting scanner without proxy")
    
    # Add custom payload option
    payload = input("\nEnter custom XSS payload (or press Enter for default): ").strip()
    if payload:
        cmd.extend(['--payload', payload])
    
    print("\nCommand to execute:")
    print(" ".join(cmd))
    print("\n" + "=" * 50)
    
    # Confirm execution
    response = input("Execute scanner? (Y/n): ")
    if response.lower() == 'n':
        print("Cancelled.")
        sys.exit(0)
    
    # Execute the scanner
    try:
        print("Starting Blind XSS Scanner...")
        print("Monitor requests in Burp Suite if proxy is enabled.")
        print("Press Ctrl+C to stop the scanner.\n")
        
        result = subprocess.run(cmd, check=False)
        
        if result.returncode == 0:
            print("\n✓ Scanner completed successfully!")
            print("Check burp_results.json for detailed results.")
            if burp_proxy:
                print("Review captured requests in Burp Suite.")
        else:
            print(f"\n⚠ Scanner exited with code: {result.returncode}")
            
    except KeyboardInterrupt:
        print("\n\n⚠ Scanner interrupted by user.")
    except Exception as e:
        print(f"\n✗ Error running scanner: {e}")

if __name__ == "__main__":
    main()
