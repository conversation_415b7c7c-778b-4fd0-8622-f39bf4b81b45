#!/usr/bin/env python3
"""
Demo script showing how the Blind XSS Scanner works
This creates a limited demonstration with a few URLs
"""

import json
import requests
import time
from blind_xss_scanner import BlindXSSScanner

def create_demo_urls():
    """Create a small set of demo URLs for testing"""
    demo_urls = [
        "https://httpbin.org/headers",
        "https://httpbin.org/get",
        "https://httpbin.org/user-agent",
        "https://httpbin.org/ip",
        "https://httpbin.org/anything"
    ]
    
    with open('demo_urls.txt', 'w') as f:
        for url in demo_urls:
            f.write(url + '\n')
    
    print(f"Created demo_urls.txt with {len(demo_urls)} URLs")
    return demo_urls

def demo_manual_testing():
    """Demonstrate manual XSS header injection"""
    print("\n🔍 Manual XSS Header Injection Demo")
    print("=" * 40)
    
    test_url = "https://httpbin.org/headers"
    xss_payload = '<script>alert("XSS-Test")</script>'
    
    # Test different headers
    test_headers = {
        'User-Agent': f'Mozilla/5.0 {xss_payload}',
        'X-Forwarded-For': f'127.0.0.1{xss_payload}',
        'Referer': f'https://google.com{xss_payload}'
    }
    
    print(f"Testing URL: {test_url}")
    print(f"XSS Payload: {xss_payload}")
    print("\nTesting headers:")
    
    for header_name, header_value in test_headers.items():
        try:
            print(f"\n📤 Testing header: {header_name}")
            
            response = requests.get(
                test_url,
                headers={header_name: header_value},
                timeout=10,
                proxies={'http': 'http://127.0.0.1:8080', 'https': 'http://127.0.0.1:8080'} if check_proxy() else {}
            )
            
            print(f"   Status: {response.status_code}")
            print(f"   Response time: {response.elapsed.total_seconds():.2f}s")
            
            # Check if our payload appears in the response
            if xss_payload in response.text:
                print(f"   🚨 PAYLOAD REFLECTED in response!")
            else:
                print(f"   ✓ Payload not reflected (expected for httpbin)")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        time.sleep(1)  # Rate limiting

def check_proxy():
    """Check if Burp proxy is available"""
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex(('127.0.0.1', 8080))
        sock.close()
        return result == 0
    except:
        return False

def demo_scanner_workflow():
    """Demonstrate the scanner workflow"""
    print("\n🤖 Automated Scanner Demo")
    print("=" * 30)
    
    # Create demo URLs
    demo_urls = create_demo_urls()
    
    # Create a custom scanner instance for demo
    print("\nInitializing scanner...")
    
    proxy_url = "http://127.0.0.1:8080" if check_proxy() else None
    if proxy_url:
        print(f"✓ Using Burp proxy: {proxy_url}")
    else:
        print("⚠ No proxy detected, running without proxy")
    
    # Simulate what the scanner does
    print(f"\n📋 Would test {len(demo_urls)} URLs")
    print("📋 With 13 headers from headers.txt")
    print(f"📋 Total combinations: {len(demo_urls) * 13}")
    
    # Show headers that would be tested
    try:
        with open('headers.txt', 'r') as f:
            headers = []
            for line in f:
                if ':' in line:
                    header_name = line.split(':')[0].strip()
                    headers.append(header_name)
        
        print(f"\n📤 Headers to test:")
        for i, header in enumerate(headers[:5], 1):  # Show first 5
            print(f"   {i}. {header}")
        if len(headers) > 5:
            print(f"   ... and {len(headers) - 5} more")
            
    except Exception as e:
        print(f"Error reading headers: {e}")

def main():
    print("🎯 Blind XSS Scanner Demonstration")
    print("=" * 50)
    
    print("\nThis demo shows how the scanner works without")
    print("overwhelming any target with requests.")
    
    # Check proxy status
    if check_proxy():
        print("\n✓ Burp Suite proxy detected at http://127.0.0.1:8080")
        print("  All requests will be visible in Burp Suite")
    else:
        print("\n⚠ Burp Suite proxy not detected")
        print("  Start Burp Suite on 127.0.0.1:8080 to see requests")
    
    print("\nChoose demo mode:")
    print("1. Manual header injection demo (5 requests)")
    print("2. Scanner workflow demonstration (no requests)")
    print("3. Both")
    
    try:
        choice = input("\nEnter choice (1-3): ").strip()
        
        if choice in ['1', '3']:
            demo_manual_testing()
        
        if choice in ['2', '3']:
            demo_scanner_workflow()
            
        print("\n✅ Demo completed!")
        print("\nTo run the full scanner:")
        print("python3 blind_xss_scanner.py --proxy http://127.0.0.1:8080")
        
    except KeyboardInterrupt:
        print("\n\n⚠ Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")

if __name__ == "__main__":
    main()
